<?php
// Auto-generated category index
// Category: writings

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Writings Index';
$meta_description = 'Writings Index - A. A. Chips';
$meta_keywords = 'writings, posts, A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$related_posts = [];

// Category posts data
$category_posts = array (
  0 => 
  array (
    'title' => 'Ode to the Decomposers - Why We Should Aspire to Be More Like Mushrooms',
    'author' => 'A. A. Chips',
    'date' => '2025-06-06',
    'excerpt' => 'Exploring the metaphor of mushrooms and decomposition in relation to societal and environmental issues.',
    'url' => 'ode-decomposers.php',
    'tags' => 
    array (
      0 => 'inspiration',
      1 => 'writings',
      2 => 'mushrooms',
      3 => 'decomposition',
      4 => 'waste',
      5 => 'regeneration',
    ),
    'filename' => 'ode-decomposers',
    'thumbnail' => NULL,
  ),
  1 => 
  array (
    'title' => 'Bite-Sized Learning for Everyone: Introducing Knowledge "Chips"',
    'author' => 'A. A. Chips',
    'date' => '2025-02-21',
    'excerpt' => 'Have you ever felt overwhelmed by the internet? You go online to learn something new, and suddenly you\'re drowning in hour-long videos, 50-page articles, and endless clickbait. It\'s like trying to drink from a firehose!',
    'url' => 'bite-sized-learning.php',
    'tags' => 
    array (
      0 => 'professionaldev',
      1 => 'aachips',
      2 => 'markdown',
    ),
    'filename' => 'bite-sized-learning',
    'thumbnail' => NULL,
  ),
  2 => 
  array (
    'title' => 'Here\'s what you can do when you feel unsupported by an organization',
    'author' => 'A. A. Chips',
    'date' => '2023-09-09',
    'excerpt' => 'We\'ve all been there. That feeling of being left to figure things out on your own, whether it\'s a project at work, a personal challenge, or navigating a complex system. When the support you expect isn\'t there, it can be frustrating, demotivating, and even make it harder to succeed.',
    'url' => 'build-your-own-supports.php',
    'tags' => 
    array (
      0 => 'advocacy',
      1 => 'accessibility',
      2 => 'addiction',
      3 => 'CompassionateCities',
    ),
    'filename' => 'build-your-own-supports',
    'thumbnail' => NULL,
  ),
  3 => 
  array (
    'title' => 'Indian Child Welfare Act of 1978 (ICWA)',
    'author' => 
    array (
    ),
    'date' => '2022-09-07',
    'excerpt' => '*"I can\'t think of a law that I apply daily that allows me to do the right thing more often. It\'s crucial and all judges need to understand it and try...',
    'url' => 'icwa-1978.php',
    'tags' => 
    array (
      0 => 'writings',
      1 => 'advocacy',
      2 => 'alienation',
      3 => 'homework',
    ),
    'filename' => 'icwa-1978',
    'thumbnail' => NULL,
  ),
  4 => 
  array (
    'title' => 'Ten Reasons I Hate Working in Restaurants',
    'author' => 'A. A. Chips',
    'date' => '2021-04-06',
    'excerpt' => 'My intention in sharing these observations is not merely to complain but to initiate a discussion about potential structural improvements that could benefit both workers and the industry as a whole.',
    'url' => '10-reasons-i-hate-restaurants.php',
    'tags' => 
    array (
      0 => 'writings',
      1 => 'aachips',
    ),
    'filename' => '10-reasons-i-hate-restaurants',
    'thumbnail' => NULL,
  ),
  5 => 
  array (
    'title' => 'I\'ve been bed-free for years. Here are three different types of Hammocks you can set up where you sleep.',
    'author' => 'A. A. Chips',
    'date' => '2018-06-06',
    'excerpt' => 
    array (
    ),
    'url' => 'bed-free.php',
    'tags' => 
    array (
      0 => 'homeless',
      1 => 'journal',
    ),
    'filename' => 'bed-free',
    'thumbnail' => NULL,
  ),
  6 => 
  array (
    'title' => 'Nostalgia: A Poem',
    'author' => 'A. A. Chips',
    'date' => '2011-11-27',
    'excerpt' => 'I wrote this poem in 2011 while attending school near a former mental hospital. I was talking a Psychology course and we talked about the ways Nostalgia has been defined over the past few hundred years. This is a poem about that.',
    'url' => 'Nostalgia - A poem.php',
    'tags' => 
    array (
      0 => 'writings',
      1 => 'poems',
      2 => 'personal',
    ),
    'filename' => 'Nostalgia - A poem',
    'thumbnail' => NULL,
  ),
);

// Generate content
ob_start();
?>
<div class="category-index">
    <header class="category-header">
        <h1><?php echo htmlspecialchars($page_title); ?></h1>
    </header>

    <div class="category-content">
        <?php echo <<<'HTML'
<h2>Writing Vault</h2>

<p>I write. Not because I like writing or anything. Well it's better than talking. That's for sure. I write to convey experiences and ideas. Sometimes I use Artificial Intelligence to refine what I write. It's because I've been told numerous times I don't have tact. By ex's and family members. The reality is that, I have so much tact. I have the most tact of anyone alive. No one has ever had more tact than I have. So if I write a bit like a robot, it's because there's a chip in my brain. Many chips. More chips than anyone has ever seen. Also lots of worms and bugs, too. But I also have good ideas sometimes. A broken clock is right twice a day, right? I'm not saying I'm a broken clock. Just a clock that's a little slower than the other clocks. Crap, I'm doing it again. So about this list. Some of these items are literal school homework that I hijacked to make my own.</p>

HTML;
        ?>
    </div>

    <div class="post-grid">
        <?php foreach ($category_posts as $post): ?>
            <div class="post-card">
                <a href="<?php echo htmlspecialchars($post['url']); ?>" class="post-card-link">
                <div class="post-card-thumbnail">
                    <?php if (isset($post['thumbnail']) && $post['thumbnail']): ?>
                        <?php if (preg_match('/^https?:\/\//', $post['thumbnail'])): ?>
                            <img src="<?php echo htmlspecialchars($post['thumbnail']); ?>" 
                                 alt="<?php echo htmlspecialchars($post['title']); ?>" class="post-thumb-img">
                        <?php else: ?>
                            <img src="<?php echo $paths['base_path']; ?>img/thumbs/<?php echo htmlspecialchars($post['thumbnail']); ?>" 
                                 alt="<?php echo htmlspecialchars($post['title']); ?>" class="post-thumb-img">
                        <?php endif; ?>
                    <?php else: ?>
                        <?php $placeholder_images = ['placeholder1.jpg', 'placeholder2.jpg', 'placeholder3.jpg', 'placeholder4.jpg']; ?>
                        <?php $random_placeholder = $placeholder_images[array_rand($placeholder_images)]; ?>
                        <img src="<?php echo $paths['base_path']; ?>img/thumbs/<?php echo $random_placeholder; ?>" 
                             alt="<?php echo htmlspecialchars($post['title']); ?>" class="post-thumb-img placeholder">
                    <?php endif; ?>
                </div>
                <div class="post-card-content">
                    <h3 class="post-card-title"><?php echo htmlspecialchars($post['title']); ?></h3>
                    <p class="post-card-excerpt"><?php echo htmlspecialchars($post['excerpt']); ?></p>
                    <div class="post-card-meta">
                        <?php if (isset($post['author']) && $post['author']): ?>
                            <span class="post-author">By <?php echo htmlspecialchars($post['author']); ?></span>
                        <?php endif; ?>
                        <?php if (isset($post['date']) && $post['date']): ?>
                            <span class="post-date"><?php echo is_string($post['date']) ? htmlspecialchars($post['date']) : ''; ?></span>
                        <?php endif; ?>
                    </div>
                    <?php if (!empty($post['tags'])): ?>
                        <div class="post-card-tags">
                            <?php foreach ($post['tags'] as $tag): ?>
                                <span class="tag"><?php echo htmlspecialchars($tag); ?></span>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
                </a>
            </div>
        <?php endforeach; ?>
    </div>
</div>
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>