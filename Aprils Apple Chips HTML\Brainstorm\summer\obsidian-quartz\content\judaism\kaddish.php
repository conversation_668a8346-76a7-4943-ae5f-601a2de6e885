<?php
// Auto-generated blog post
// Source: kaddish.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Kaddish for the Soul of Judaism';
$meta_description = 'As I write this, the wheels of genocide are turning. As I write this, I am preparing for Shabbat. When I see Gaza, I see my own people languishing in concentration camps. I see a world that has turned its back on us, letting us be slaughtered en masse because we aren’t quite human enough. I am having a nightmare, can you wake me up?';
$meta_keywords = 'jewish, palestine, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Kaddish for the Soul of Judaism',
  'author' => 'Amanda Gellender',
  'tags' => 
  array (
    0 => 'jewish',
    1 => 'palestine',
  ),
  'date' => '2024-02-01',
  'excerpt' => 'As I write this, the wheels of genocide are turning. As I write this, I am preparing for Shabbat. When I see Gaza, I see my own people languishing in concentration camps. I see a world that has turned its back on us, letting us be slaughtered en masse because we aren’t quite human enough. I am having a nightmare, can you wake me up?',
  'categories' => 
  array (
    0 => 'Jewish',
    1 => 'Journal',
  ),
  'source_file' => 'content\\judaism\\kaddish.md',
);

// Raw content
$post_content = '<p>Today is Day 117 of the bloodiest invasion inflicted in my lifetime. Over 30,000 civilians, mostly children, with no escape route or recourse have been murdered by a country I have been taught most of my life to support unconditionally. I have been glued to the legal proceedings in the International Court of Justice since the beginning of this year. Despite provisional rulings ordering for the invasion to stop, it has only gotten worse. This is the most well-documented genocide that has ever happened. Journalists, hospitals, children, no one is safe.</p>

<p>There is a great article written by Amanda Gellender, an American Jew, titled <a href="https://agelender.medium.com/kaddish-for-the-soul-of-judaism-genocide-in-palestine-1248d7ff2611" class="external-link">Kaddish for the Soul of Judaism</a>. I have it in print and have read it a dozen times for comfort and solace. I recommend every word.</p>

<p>While many like to frame the issue as a conflict between Judaism and Islam, this couldn’t be farther from the truth. Jews and Muslims have been good neighbors and friends for a very long time. Our religions and cultures are similar. We refrain from pork and shellfish, value modesty, and both are born from semitic languages. To be a Semite includes both Arabic and Hebrew. They are similar languages. <a href="https://www.nationalgeographic.com/history/article/hebrew-wasnt-spoken-for-2000-years-heres-how-it-was-revived" class="external-link">Hebrew was extinct and unspoken as a language for 1,500 years</a>. Arabic wasn’t. Both Jews and Muslims have keys to each other’s past, culture, and future.</p>

<p>War, Occupation, and Racism have only been a theme and conflict between us in the past century and it has more to do with the power interests of Europe, empire, and western expansion. It’s the people over there now and our children tomorrow.</p>

<p>_____________________________________</p>
<img src="../img/letGazaLive.jpg" alt="letGazaLive.jpg">

<p>Excerpt from Amanda Gellender’s article:</p>

<p>“As I write this, the wheels of genocide are turning. As I write this, I am preparing for Shabbat.</p>

<p>When I see Gaza, I see my own people languishing in concentration camps. I see a world that has turned its back on us, letting us be slaughtered en masse because we aren’t quite human enough.</p>

<p>I am having a nightmare, can you wake me up? I’m dreaming that the Star of David is not sewn onto our clothes but affixed to the genocidal soldiers who bomb hospitals, schools, and churches with no regard for life. Who round up entire communities and shoot kids dead in the street.</p>

<p>I remember there was talk of Tikkun Olam and Tzedakah — is our humanity buried somewhere under the rubble? Is anyone still breathing under there? I scour bombed remnants of residential buildings for shreds of Judaism’s soul. I thought it was still here. I thought so many things that evaporated in the dust of an air strike.</p>

<p>We built institutions and monuments to “never forget” but look at us now. It seems we left our humanity outside the door of the holocaust memorial museum. Jews are a diasporic miracle trapped in a nation state of lies. Israel has sacrificed our humanity at the altar of nationalism.”</p>

<p>—---------------------------------------------------------</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>