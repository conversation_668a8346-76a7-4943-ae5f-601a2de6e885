<?php
// Auto-generated blog post
// Source: coding-troll-crt-line.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Black Coder Trolls Critical Race Theory Tip Line Until it Quietly Shuts Down';
$meta_description = 'When Virginia Governor <PERSON> established a tip line to report teachers allegedly teaching critical race theory (CRT) in schools, it sparked immediate backlash. Critics argued it was a thinly veiled attempt to intimidate educators. However, it was the ingenuity of a young developer, Sofia Ongele, that ultimately led to its demise.';
$meta_keywords = 'inspiration, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Black Coder Trolls Critical Race Theory Tip Line Until it Quietly Shuts Down',
  'date' => 
  array (
  ),
  'excerpt' => 'When Virginia Governor Glenn Youngkin established a tip line to report teachers allegedly teaching critical race theory (CRT) in schools, it sparked immediate backlash. Critics argued it was a thinly veiled attempt to intimidate educators. However, it was the ingenuity of a young developer, Sofia Ongele, that ultimately led to its demise.',
  'url' => 'https://www.youtube.com/watch?v=i1WVdcSolUU',
  'categories' => 
  array (
    0 => 'Inspiration',
  ),
  'tags' => 
  array (
    0 => 'inspiration',
  ),
  'source_file' => 'content\\inspiration\\coding-troll-crt-line.md',
);

// Raw content
$post_content = '<p><a href="https://www.youtube.com/watch?v=i1WVdcSolUU" class="external-link">Black Coder Trolls Critical Race Theory Tip Line Until It Quietly Shuts Down</a></p>

<iframe width="560" height="315" src="https://www.youtube.com/embed/i1WVdcSolUU?si=-wWtznDhEqLNfuwl" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>

<h2>How Gen Z Shut Down a Controversial CRT Tip Line Using the Power of the Internet</h2>

<p>When Virginia Governor Glenn Youngkin established a tip line to report teachers allegedly teaching critical race theory (CRT) in schools, it sparked immediate backlash. Critics argued it was a thinly veiled attempt to intimidate educators. However, it was the ingenuity of a young developer, Sofia Ongele, that ultimately led to its demise.</p>

<p>Ongele, a digital strategist for the youth-led nonprofit Gen-Z for Change and a student at Columbia University, created a website that automatically spammed the tip line with song lyrics and lines from the Bee Movie. This clever tactic effectively overwhelmed the system, rendering it useless.</p>

<p>In a discussion on Indisputable with Dr. Rashad Richey and Yasmin Aliya Khan, the story of Ongele\'s activism was highlighted. The video, which can be viewed here: <a href="https://www.businessinsider.com/tiktokers-spamming-virginia-youngkin-critical-race-theory-tip-line-2022-1" class="external-link">https://www.businessinsider.com/tiktokers-spamming-virginia-youngkin-critical-race-theory-tip-line-2022-1</a>, details how Ongele\'s tool allowed users to input a real Virginia school and city, generating a nonsensical report. As Ongele stated, the aim was to match the "unserious" nature of the tip line itself.</p>

<p>The initiative quickly gained traction after Ongele shared the website with her colleagues at Gen-Z for Change. Their coordinated social media campaign resulted in thousands of submissions from across the country and even the globe, causing the tip line to become inoperable within 48 hours. Months later, it was quietly shut down.</p>

<p>This successful endeavor underscores the power of digital activism and the resourcefulness of young people in addressing issues they find unjust. As Ongele powerfully stated, "We were far more powerful than a couple people in official positions of power." Her work exemplifies how technology can be leveraged to combat perceived overreach and make civic engagement more accessible and impactful.</p>

<p>Sofia Ongele\'s dedication to creating positive change through technology is evident in her past work as well. At 17, she developed an award-winning app to support survivors of sexual violence, demonstrating a consistent commitment to serving marginalized communities. Her role at Gen-Z for Change further solidifies her as a rising force in digital activism.</p>

<p>This story serves as an inspiring example of how creativity, technology, and collective action can effectively challenge initiatives perceived as harmful. It highlights the growing influence of digitally native generations in shaping civic discourse and holding those in power accountable.</p>


<p>https://redawn.en.softonic.com/iphone</p>';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>