<?php
// Auto-generated blog post
// Source: liturgy-of-the-drowned.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'The First Liturgy of the Drowned
"Date:": May 30, 2025';
$meta_description = '';
$meta_keywords = 'palestine, spiritual, advocacy, writings, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'The First Liturgy of the Drowned
"Date:": May 30, 2025',
  'tags' => 
  array (
    0 => 'palestine',
    1 => 'spiritual',
    2 => 'advocacy',
    3 => 'writings',
  ),
  'author' => 'A. A. Chips',
  'categories' => 
  array (
    0 => 'Writings',
    1 => 'Inspiration',
    2 => 'Journal',
    3 => 'Climate',
  ),
  'source_file' => 'content\\judaism\\liturgy-of-the-drowned.md',
);

// Raw content
$post_content = '<p>#palestine #spiritual #CompassionateCities</p>
<h3> The First Liturgy of the Drowned</h3>

<img src="../img/bottleboat.jpg" alt="bottleboat.jpg">

<p><strong>2031. The California Delta.</strong></p>

<p>The priest wore a bulletproof vest under her robe. Not for fear of soldiers—the state had abandoned this stretch of drowned farmland years ago—but because the drones of the agribusiness cartels still patrolled, hunting for squatters who dared to detoxify the poisoned soil.</p>

<p>She raised her arms. Around her, fifty people knelt in the muck, their hands pressing into the wet earth. A few feet away, a makeshift desalinator hummed, sucking boron and arsenic from the groundwater.</p>

<p>_"We bear witness,"_ she began. The words weren’t hers; they’d been crowdsourced from Gaza, from the Congo, from the last Standing Rock holdouts. A liturgy written by the internet’s grief.</p>

<p>A teenager in the front row lifted a smartphone, its screen cracked. On it played a looped video: his sister, killed in a climate camp bombing a decade ago. He’d spliced her face into every frame of the livestream, a ghost in the machine.</p>

<p>_"We bear witness to the martyrs of thirst,"_ the crowd chanted. _"To the martyrs of fire. To the martyrs of the algorithm."_</p>

<p>The priest unclipped a vial from her belt. Inside, a slurry of mycorrhizal fungi, stolen from a university lab. She poured it into the dirt. _"This was the sin,"_ she said. _"To treat the earth as dead. But what is resurrection if not the mycelium finding the broken root?"_</p>

<p>A drone buzzed overhead. The teenager stood, hefting a signal jammer welded from old cellphones. The screen flickered—_connection lost_—and the machine spiraled into the creek. Someone cheered.</p>

<p>_"The old world taught us to pray alone,"_ the priest said. _"But the first rule of the new liturgy is this: No one kneels without hands to pull them up."_</p>

<p>The work began. Some planted reeds to anchor the eroding bank. Others passed a flask of filtered water, each sip a sacrament. The teenager synced his sister’s face to a projector; her smile flickered against the levee wall, a defiance of shadows.</p>

<p>By dawn, the drone’s carcass would be repurposed into a wind sensor. By year’s end, the creek would run clear enough for frogs to return.</p>

<p>And somewhere, in a city still clinging to the myth of borders, a bureaucrat would mark the area on a map: _"Uninhabitable."_</p>

<p>But the land, at last, was learning to breathe again.</p>

<img src="../img/swancpr.jpg" alt="swancpr.jpg">';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>