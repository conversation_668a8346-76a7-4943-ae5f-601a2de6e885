<?php
// Initialize path constants if not already done
if (!isset($paths) && isset($config)) {
    require_once (isset($base_url) ? $base_url : '') . 'path-helper.php';
    $paths = initPaths($config, __FILE__);

    // Set fallback variables for backward compatibility
    if (!isset($css_path)) $css_path = $paths['css_path'];
    if (!isset($js_path)) $js_path = $paths['js_path'];
    if (!isset($base_url)) $base_url = $paths['base_path'];
}
?>
<!DOCTYPE html>
<html lang="<?php echo $config['site']['language'] ?? 'en'; ?>">

<head>
    <meta charset="<?php echo $config['site']['charset'] ?? 'UTF-8'; ?>">
    <meta name="viewport" content="<?php echo $config['meta']['viewport'] ?? 'width=device-width, initial-scale=1.0'; ?>">

    <!-- Title -->
    <title><?php
        if (isset($page_title)) {
            echo htmlspecialchars($page_title) . ' - ' . htmlspecialchars($config['site']['title'] ?? 'A. A. Chips\' Blog');
        } else {
            echo htmlspecialchars($config['site']['title'] ?? 'A. A. Chips\' Blog');
        }
    ?></title>

    <!-- Meta Description -->
    <meta name="description" content="<?php
        echo htmlspecialchars($meta_description ?? $config['site']['description'] ?? '');
    ?>">

    <!-- Meta Keywords -->
    <meta name="keywords" content="<?php
        echo htmlspecialchars($meta_keywords ?? $config['meta']['keywords'] ?? '');
    ?>">

    <!-- Author -->
    <meta name="author" content="<?php echo htmlspecialchars($config['site']['author'] ?? 'A. A. Chips'); ?>">

    <!-- Robots -->
    <meta name="robots" content="<?php echo $config['meta']['robots'] ?? 'index, follow'; ?>">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="<?php echo ($css_path ?? $paths['css_path'] ?? 'css/') . 'style.css?v=' . time(); ?>">

    <!-- External Services -->
    <?php if ($config['social']['chatway_enabled'] ?? false): ?>
    <!-- Chatway -->
    <?php endif; ?>

    <?php if ($config['social']['clarity_enabled'] ?? false): ?>
    <!-- Microsoft Clarity -->
    <?php endif; ?>
</head>

  <body>
    <a href="#main-content" class="skip-link">Skip to main content</a>
    <?php include ($paths['includes_path'] ?? 'includes/') . 'header.php'; ?>

      <div class="main-container">
          <main id="main-content" tabindex="-1">
              <div class="content">
                  <?php if (isset($content)) echo $content; ?>
              </div>

              <!-- Browse Categories Section -->
              <section class="browse-categories">
                  <h2>Browse Categories</h2>
                  <div class="categories-grid">
                      <?php if (isset($related_posts) && !empty($related_posts)): ?>
                          <?php foreach ($related_posts as $post): ?>
                              <a href="<?php echo $post['url']; ?>" class="category-card">
                                  <h3><?php echo htmlspecialchars($post['title']); ?></h3>
                                  <?php if (isset($post['excerpt'])): ?>
                                      <p><?php echo htmlspecialchars($post['excerpt']); ?></p>
                                  <?php endif; ?>
                              </a>
                          <?php endforeach; ?>
                      <?php else: ?>
                          <a href="<?php echo ($base_url ?? '') . 'content/alienation/index.php'; ?>" class="category-card">
                              <h3>Alienation</h3>
                              <p>Family alienation and recovery content.</p>
                          </a>
                          <a href="<?php echo ($base_url ?? '') . 'content/humor/index.php'; ?>" class="category-card">
                              <h3>Humor</h3>
                              <p>Funny stories and observations.</p>
                          </a>
                          <a href="<?php echo ($base_url ?? '') . 'content/inspiration/index.php'; ?>" class="category-card">
                              <h3>Inspiration</h3>
                              <p>Uplifting content and motivational pieces.</p>
                          </a>
                          <a href="<?php echo ($base_url ?? '') . 'content/journal/index.php'; ?>" class="category-card">
                              <h3>Journal</h3>
                              <p>Personal reflections and daily thoughts.</p>
                          </a>
                          <a href="<?php echo ($base_url ?? '') . 'content/street/index.php'; ?>" class="category-card">
                              <h3>Street Advocacy</h3>
                              <p>Homelessness advocacy and social justice.</p>
                          </a>
                          <a href="<?php echo ($base_url ?? '') . 'content/judaism/index.php'; ?>" class="category-card">
                              <h3>Judaism</h3>
                              <p>Jewish identity and spiritual reflections.</p>
                          </a>
                      <?php endif; ?>
                  </div>
              </section>
          </main>

          <aside class="sidebar">
              <?php include ($paths['includes_path'] ?? 'includes/') . 'sidebar.php'; ?>
          </aside>
      </div>
      <?php include ($paths['includes_path'] ?? 'includes/') . 'footer.php'; ?>

      <!-- Modal for shorthand content -->
      <div id="contentModal" class="modal" style="display: none;">
          <div class="modal-content">
              <span class="close">&times;</span>
              <div id="modalBody"></div>
          </div>
      </div>

      <script src="<?php echo ($js_path ?? $paths['js_path'] ?? 'js/') . 'script.js'; ?>"></script>
      <script src="<?php echo ($js_path ?? $paths['js_path'] ?? 'js/') . 'modal.js'; ?>"></script>
  </body>
</html>
