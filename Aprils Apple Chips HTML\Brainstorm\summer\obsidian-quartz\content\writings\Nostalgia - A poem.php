<?php
// Auto-generated blog post
// Source: Nostalgia - A poem.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Nostalgia: A Poem';
$meta_description = 'I wrote this poem in 2011 while attending school near a former mental hospital. I was talking a Psychology course and we talked about the ways Nostalgia has been defined over the past few hundred years. This is a poem about that.';
$meta_keywords = 'writings, poems, personal, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Nostalgia: A Poem',
  'author' => 'A. A. Chips',
  'date' => '2011-11-27',
  'excerpt' => 'I wrote this poem in 2011 while attending school near a former mental hospital. I was talking a Psychology course and we talked about the ways Nostalgia has been defined over the past few hundred years. This is a poem about that.',
  'categories' => 
  array (
    0 => 'Writings',
  ),
  'tags' => 
  array (
    0 => 'writings',
    1 => 'poems',
    2 => 'personal',
  ),
  'source_file' => 'content\\writings\\Nostalgia - A poem.md',
);

// Raw content
$post_content = '<p><em>I wrote this poem in 2011 while attending school near a former mental hospital. I was talking a Psychology course and we talked about the ways Nostalgia has been defined over the past few hundred years. This is a poem about that.</em></p>

<h2>Nostalgia</h2>
<p>by A. A. Chips</p>

<p>A cerebro-neurological disorder caused by the quite continuous vibration of animal spirits through those fibers of the middle brain in which impressions, ideas, and traces, of the Fatherland still cling.</p>

<p>1688.</p>

<p>Sharp “atmospheric pressure differences, blood migrates from the heart to the brain, afflicting the observed with sentiment.</p>

<p>1732.</p>

<p>Indigenous to the Alpines, unremitting clanging of cowbells destroys the eardrum and brain cells.</p>

<p>Too much cowbell.</p>

<p>Anxiety.</p>

<p>Sadness.</p>

<p>Weakness.</p>

<p>Loss of Appetite.</p>

<p>Insomnia.</p>

<p>Fear.</p>

<p>Obsessive Homesickness.</p>

<p>Intense unhappiness.</p>

<p>Immigrant psychosis for the soldiers, seamen, immigrants, and first year college students.</p>

<p>I the protagonist.</p>

<p>Featuring them.</p>

<p>A way to cope with the present.</p>';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>