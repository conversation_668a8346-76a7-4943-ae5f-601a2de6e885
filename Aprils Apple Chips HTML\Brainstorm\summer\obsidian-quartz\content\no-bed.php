<?php
// Auto-generated blog post
// Source: no-bed.md

// Load configuration
$config = include '../config.php';

// Page variables
$page_title = 'How I Sleep Without a Bed';
$meta_description = 'Personal experiences and practical advice on finding rest and comfort while experiencing homelessness';
$meta_keywords = 'homelessness, survival, personal, practical-tips, draft, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = '../css/';
$js_path = '../js/';
$base_url = '../';
$related_posts = [
    ['title' => 'Alienation', 'url' => '../content/alienation/index.php', 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => '../content/climate/index.php', 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => '../content/humor/index.php', 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => '../content/inspiration/index.php', 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => '../content/journal/index.php', 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => '../content/judaism/index.php', 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => '../content/kitchen/index.php', 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Street', 'url' => '../content/street/index.php', 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => '../content/writings/index.php', 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'How I Sleep Without a Bed',
  'date' => '2025-05-20',
  'tags' => 
  array (
    0 => 'homelessness',
    1 => 'survival',
    2 => 'personal',
    3 => 'practical-tips',
    4 => 'draft',
  ),
  'excerpt' => 'Personal experiences and practical advice on finding rest and comfort while experiencing homelessness',
  'source_file' => 'content\\no-bed.md',
);

// Raw content
$post_content = '<h1>How I Sleep Without a Bed</h1>

<h2>Reflection Questions</h2>
<ul><li>What creative solutions have you discovered for sleeping comfortably without traditional housing?</li>
<p><li>How has your experience with alternative sleeping arrangements changed your perspective on comfort and necessity?</li></p>
<p><li>What practical advice would be most valuable to others facing similar circumstances?</li></p>
<p><li>How do sleep challenges affect other aspects of life when experiencing homelessness?</li></p>

<h2>Initial Thoughts</h2>
<p>Sleep is a fundamental human need, yet finding safe and comfortable places to rest can be one of the biggest challenges when experiencing homelessness. This document can explore:</p>
<p><li>Creative and practical solutions for sleeping in vehicles, outdoors, or in temporary shelters</li></p>
<p><li>Safety considerations and best practices</li></p>
<p><li>Seasonal adaptations (staying warm in winter, cool in summer)</li></p>
<p><li>The emotional and physical impact of sleep deprivation and instability</li></p>

<h2>Practical Tips</h2>
<p>[Space for practical advice on sleeping arrangements, comfort techniques, and safety measures]</p>

<h2>Personal Experiences</h2>
<p>[Space for personal stories and reflections on sleeping without traditional housing]</p>

<h2>Resources</h2>
<p>[Space for listing helpful resources, locations, or organizations that can assist with sleep needs]</p>

<h2>Related Content</h2>
<p><li><a href="car-partment.php" class="internal-link">car-partment</a></li></p>
<p><li><a href="stay-warm.php" class="internal-link">stay-warm</a></li></p>
<p><li><a href="living-in-suv-means-no-indoor-plumbing.php" class="internal-link">Living in SUV Means no Indoor Plumbing</a></li></p>
<p><li><a href="homelessness-timeline.php" class="internal-link">Homelessness Timeline</a></li></ul></p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include '../page template.htm';
?>