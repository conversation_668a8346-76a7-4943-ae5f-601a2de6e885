<?php
// Auto-generated blog post
// Source: bed-free.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'I\'ve been bed-free for years. Here are three different types of Hammocks you can set up where you sleep.';
$meta_description = array (
);
$meta_keywords = 'homeless, journal, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'I\'ve been bed-free for years. Here are three different types of Hammocks you can set up where you sleep.',
  'author' => 'A. A. Chips',
  'categories' => 
  array (
    0 => 'Journal',
    1 => 'Inspiration',
  ),
  'tags' => 
  array (
    0 => 'homeless',
    1 => 'journal',
  ),
  'date' => '2018-06-06',
  'excerpt' => 
  array (
  ),
  'source_file' => 'content\\writings\\bed-free.md',
);

// Raw content
$post_content = '<p>Something about me is that I hate mattresses. With a burning passion.</p>

<img src="mattress.png" alt="my crazy dad lights my mattress on fire! Youtube video thumbnail">

<p>Seriously they suck. They are uncomfortable. They are lumpy and clumpy. You have to change sheets on them all the time. They are a breeding ground for dust mites. A fire hazard. A pain in the butt to move. A pain in the butt to dispose of. Do you know how many old smelly mattresses populate landfills? I don\'t know and I don\'t want to know.</p>

<p>Want to know what you can sleep in that isn\'t prone to any of those issues? A hammock.</p>

<p>Look, go into this with an open mind. I know. You probably tried sleeping in a hammock and fell out, or hurt your back. Chances are you were doing it completely wrong. Sorry to break the news to you.</p>

<p>Consider this blog post the poorest planned guide to sleeping in a hammock. If you are not convinced by the end of it, and have a hammock you do not wish to use, I will gladly accept it off your hands.</p>

<h2>Beware of Spreader Bar Hammocks</h2>

<p>Most people when they think of hammocks, they are thinking of Spreader Bar hammocks. Those are the ones with the wooden bars at both end. This was a result of European Spaniards seeing Native Indigenous people sleeping in hammocks and trying to recreate it. They wanted it to resemble more of a bed, because that is what they were used to. These aren\'t terrible. But they are prone to flipping over. And they aren\'t that comfortable to sleep in either.</p>

<img src="spreaderbar.jpg" alt="Spreader Bar Hammock">

<h2>It\'s supposed to sag</h2>

<p>A proper hammock is supposed to sag. You aren\'t supposed to rest in them straight. You are supposed to rest in them at an angle. This is what makes them so comfortable. The angle allows for proper airflow and prevents you from overheating. It also allows for proper blood flow. If you hurt your back or neck trying to rest in a hammock, chances are you didn\'t have it sagging and you didn\'t lay in it at an angle.</p>

<img src="hammock-sag.png" alt="person laying in sagged hammock outdoors.">
<h2>What to look for in a Hammock</h2>

<p>The most common and popular types of hammock are made from nylon. This is the same material used in parachutes. It is strong and durable. It is also lightweight. You can get hammocks made from other materials, but nylon is the most common. You want to look for a hammock that is at least 1000 denier. This means it is made from 1000 threads per square inch. The higher the denier, the stronger the hammock. You want to avoid anything below 600 denier. Anything below that is not going to hold up well.</p>

<p>You also want to look for a hammock that is at least 30 inches wide. Anything smaller is going to be uncomfortable. You want to be able to stretch out and relax. I\'d recommend looking for double wide, because these give lots of room to stretch out and are extremely comfortable. Singles are great too, but I would recommend if you use a single, to have something to rest your legs on.</p>

<h2>Other Materials</h2>

<p>While harder to find, the most ideal material for a hammock is rope. These are typically woven by hand and you can find them in Latin and South America. The natural fibers are extremely durable, they breathe well, but they are expensive.</p>

<p>You can get hammocks made from silk. These are also expensive, but very stretchy and soft on the skin. Typically these will come in the form of aerial and acrobatic silks. When used to sleep in, they contour to every inch of your body.</p>

<h2>Setting Up</h2>

<p>There are methods to set up hammocks both outdoors and inside. If you do not have two strong posts to secure your hammock to, you can use hammock stands. These are typically made from metal and are very sturdy. You can also use trees, but make sure they are strong enough to hold your weight. Chances are you will need to use rope to supplement the length of the hammock for ideal sag. If you are setting up hammock anchors in a wall, be sure to secure them in the studs. If you aren\'t sure how to do this safely consult a professional.</p>

<h2>Benefits</h2>

<p>There are many benefits to sleeping in a hammock. The most obvious is that it is comfortable. It is also very portable. You can take it with you wherever you go. It is also very easy to clean. You can just throw it in the washing machine with cold water. It is also very durable. You can get hammocks that will last you a lifetime. Hammocks made for two people can be extremely fun to sleep in with a partner, or with your kids. The light swinging motion is very soothing, and can help you fall asleep faster. When done right, it feels like you are floating.</p>

<h2>Price</h2>

<p>Hammocks can range in price from $20 to $200. The cheaper ones are typically made from nylon and are not as durable. The more expensive ones are typically made from rope or silk and are much more durable. I would recommend spending at least $50 on a hammock. Anything less is not going to hold up well. It\'s way cheaper than a mattress, sheets, a bed frame, a box spring, and a moving truck.</p>';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>