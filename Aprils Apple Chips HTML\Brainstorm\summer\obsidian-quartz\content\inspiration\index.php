<?php
// Auto-generated category index
// Category: inspiration

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Inspiration';
$meta_description = 'Browse all posts in the Inspiration category';
$meta_keywords = 'inspiration, posts, A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$related_posts = [];

// Category posts data
$category_posts = array (
  0 => 
  array (
    'title' => '150 years from now, none of us reading this post today will be alive',
    'author' => 'Claire Rose Elise',
    'date' => '2023-12-22',
    'excerpt' => 'Let us take life easy, nobody will get out of this world alive. . . The land you are fighting and ready to kill for, somebody left that land, the person is dead, rotten, and forgotten. That will also be your fate. In 150 years to come, none of the vehicles or phones we are using today to brag will be relevant. Biko, take life easy!',
    'url' => '150-years-from-now.php',
    'tags' => 
    array (
      0 => 'inspiration',
      1 => 'advocacy',
    ),
    'filename' => '150-years-from-now',
    'thumbnail' => '../../img/get-us-out.jpg',
  ),
  1 => 
  array (
    'title' => 'America Is a Gun',
    'author' => 'Brian Bilston',
    'date' => NULL,
    'excerpt' => 'England is a cup of tea. France, a wheel of ripened brie. Greece, a short, squat olive tree. America is a gun.',
    'url' => 'america-is-a-gun.php',
    'tags' => 
    array (
      0 => 'poems',
      1 => 'inspiration',
    ),
    'filename' => 'america-is-a-gun',
    'thumbnail' => '../../img/church-gun.png',
  ),
  2 => 
  array (
    'title' => 'Black Coder Trolls Critical Race Theory Tip Line Until it Quietly Shuts Down',
    'author' => NULL,
    'date' => 
    array (
    ),
    'excerpt' => 'When Virginia Governor Glenn Youngkin established a tip line to report teachers allegedly teaching critical race theory (CRT) in schools, it sparked immediate backlash. Critics argued it was a thinly veiled attempt to intimidate educators. However, it was the ingenuity of a young developer, Sofia Ongele, that ultimately led to its demise.',
    'url' => 'coding-troll-crt-line.php',
    'tags' => 
    array (
      0 => 'inspiration',
    ),
    'filename' => 'coding-troll-crt-line',
    'thumbnail' => NULL,
  ),
  3 => 
  array (
    'title' => 'Fundamental Principles of the Poor Peoples\' Campaign - A Call for Moral Revival',
    'author' => 'Poor People\'s Campaign - A National Call for Moral Revival',
    'date' => '2025-06-06',
    'excerpt' => 'We are rooted in a moral analysis based on our deepest religious and constitutional values that demand justice for all. Moral revival is necessary to save the heart and soul of our democracy.',
    'url' => 'principles-moral-revival.php',
    'tags' => 
    array (
      0 => 'inspiration',
      1 => 'resources',
      2 => 'advocacy',
    ),
    'filename' => 'principles-moral-revival',
    'thumbnail' => '../../img/time-to-build.png',
  ),
  4 => 
  array (
    'title' => 'I don\'t help my wife clean the house..',
    'author' => 'Mum Central',
    'date' => NULL,
    'excerpt' => 'I sat back down with him again and explained to him that I don\'t ′′help′′ my wife. Actually, my wife doesn\'t need help, she needs a partner, a teammate. I\'m her home partner… and due to that, all functions are divided, which is not “help” with household chores.',
    'url' => 'i-dont-help-my-wife.php',
    'tags' => 
    array (
      0 => 'inspiration',
    ),
    'filename' => 'i-dont-help-my-wife',
    'thumbnail' => '../../img/wifeSkills.jpg',
  ),
  5 => 
  array (
    'title' => 'If A Lemon',
    'author' => 'Nikki Giovani',
    'date' => NULL,
    'excerpt' => 'If a lemon, kissed a beet. Is it sour, or is it sweet?',
    'url' => 'if-a-lemon.php',
    'tags' => 
    array (
      0 => 'poems',
      1 => 'inspiration',
    ),
    'filename' => 'if-a-lemon',
    'thumbnail' => '../../img/child-picking-lemons-stockcake.jpg',
  ),
  6 => 
  array (
    'title' => 'The Art of Listening',
    'author' => 'Brenda Ueland',
    'date' => '2025-06-11',
    'excerpt' => 'An exploration of the power of listening and its role in creating meaningful connections with others.',
    'url' => 'art-of-listening.php',
    'tags' => 
    array (
      0 => 'listening',
      1 => 'communication',
      2 => 'relationships',
    ),
    'filename' => 'art-of-listening',
    'thumbnail' => '../../img/listening.jpg',
  ),
  7 => 
  array (
    'title' => 'Media Literacy in Finland\'s Grade School Curriculum',
    'author' => 'A. A. Chips',
    'date' => '2025-01-12',
    'excerpt' => 'By teaching students how to critically evaluate and discern bad information, Finland aims to create a more informed and discerning population. In this blog post, we’ll explore some of Finland’s media literacy initiatives and provide examples of activities that readers can engage in to test their own critical thinking skills.',
    'url' => 'finland-media-literacy.php',
    'tags' => 
    array (
      0 => 'masscommunication',
      1 => 'advocacy',
    ),
    'filename' => 'finland-media-literacy',
    'thumbnail' => NULL,
  ),
  8 => 
  array (
    'title' => 'The MOSAIC Method - A Free Assessment Tool',
    'author' => 'A. A. Chips',
    'date' => '2020-06-26',
    'excerpt' => 'I wanted to share one of my favorite free assessment tools available on the internet. It\'s called the MOSAIC Method. It was developed by Gavin De Becker, author of "The Gift of Fear." Gavin and the MOSAIC Method have been featured on Oprah. This is a tool used by large corporations and the Secret Service to evaluate the danger of specific high-risk situations and people.',
    'url' => 'mosaic-method.php',
    'tags' => 
    array (
      0 => 'safety',
      1 => 'assessment',
      2 => 'MOSAIC',
      3 => 'Gavin De Becker',
      4 => 'Gift of Fear',
    ),
    'filename' => 'mosaic-method',
    'thumbnail' => NULL,
  ),
  9 => 
  array (
    'title' => 'The Oldest Customer Complaint',
    'author' => 'A. A. Chips',
    'date' => '2020-06-26',
    'excerpt' => 'The oldest written customer complaint was stolen by the British Museum in 1953, and has lived there ever since. It is written about in the Guinness Book of World Records. This complaint is 3767 years old, and was found in the ancient city of Ur in Southern Iraq.',
    'url' => 'oldest-complaint.php',
    'tags' => 
    array (
      0 => 'masscommunication',
      1 => 'memes',
      2 => 'humor',
      3 => 'inspiration',
    ),
    'filename' => 'oldest-complaint',
    'thumbnail' => NULL,
  ),
  10 => 
  array (
    'title' => 'Jemez Principles for Democratic Organizing',
    'author' => 'The Southwest Network for Environmental and Economic Justice, 1996',
    'date' => '1996-12-08',
    'excerpt' => 'If we hope to achieve just choices that include all people in decision-making and assure that all people have an equitable share of the wealth and the work of this world, then we must work to build that.',
    'url' => 'jemez-principles.php',
    'tags' => 
    array (
      0 => 'inspiration',
      1 => 'resources',
    ),
    'filename' => 'jemez-principles',
    'thumbnail' => NULL,
  ),
  11 => 
  array (
    'title' => 'The Man in the Glass',
    'author' => 'Dale Wimbrow',
    'date' => NULL,
    'excerpt' => 'You can fool the whole world down the pathway of years, and get pats on the back as you pass, but your final reward will be heartaches and tears if you\'ve cheated the man in the glass',
    'url' => 'man-in-glass.php',
    'tags' => 
    array (
      0 => 'inspiration',
      1 => 'poems',
    ),
    'filename' => 'man-in-glass',
    'thumbnail' => '../../img/man-in-glass.jpg',
  ),
  12 => 
  array (
    'title' => 'The Paradox of our Age',
    'author' => 'Dalai Lama',
    'date' => NULL,
    'excerpt' => 'We have bigger houses, and smaller families. More convenience, but less time..',
    'url' => 'paradox-of-age.php',
    'tags' => 
    array (
      0 => 'inspiration',
    ),
    'filename' => 'paradox-of-age',
    'thumbnail' => '../../img/helpbysafaaodah.jpg',
  ),
  13 => 
  array (
    'title' => 'Ways to be helpful during a loved one\'s panic attack',
    'author' => 'Mental Health on the Mighty',
    'date' => NULL,
    'excerpt' => 'Talk to me to help keep me grounded. Once I\'m lost in my thoughts it\'s harder to get out of the panic attack. If possible, assist me to a safe spot. Don\'t undermine my panic. Don\'t yell at me. Acknowledge it. Understand. Have empathy.',
    'url' => 'help-during-panic-attack.php',
    'tags' => 
    array (
      0 => 'advocacy',
      1 => 'a11y',
      2 => 'addiction',
    ),
    'filename' => 'help-during-panic-attack',
    'thumbnail' => '../../img/gazarubble.webp',
  ),
  14 => 
  array (
    'title' => 'Why Didn\'t She Just Leave?',
    'author' => 'Anonymous',
    'date' => NULL,
    'excerpt' => '# Why Didn\'t She Just Leave? I found this reposted on a social media site and liked it. Related: If you choose to be homeless, it means your choices i...',
    'url' => 'why-didnt-she-leave.php',
    'tags' => 
    array (
      0 => 'memes',
      1 => 'dv',
      2 => 'library',
      3 => 'resources',
    ),
    'filename' => 'why-didnt-she-leave',
    'thumbnail' => '../../img/swancpr.jpg',
  ),
);

// Generate content
ob_start();
?>
<div class="category-index">
    <header class="category-header">
        <h1><?php echo htmlspecialchars($page_title); ?></h1>
    </header>

    <div class="category-content">
        <?php echo <<<'HTML'
<p>Here's a little page of sunshine for your own personal and depraved purposes. A catch all of things I save off the internet that are helpful, cool, educational, or just don't fit in the other boxes.</p>












HTML;
        ?>
    </div>

    <div class="post-grid">
        <?php foreach ($category_posts as $post): ?>
            <div class="post-card">
                <a href="<?php echo htmlspecialchars($post['url']); ?>" class="post-card-link">
                <div class="post-card-thumbnail">
                    <?php if (isset($post['thumbnail']) && $post['thumbnail']): ?>
                        <?php if (preg_match('/^https?:\/\//', $post['thumbnail'])): ?>
                            <img src="<?php echo htmlspecialchars($post['thumbnail']); ?>" 
                                 alt="<?php echo htmlspecialchars($post['title']); ?>" class="post-thumb-img">
                        <?php else: ?>
                            <img src="<?php echo $paths['base_path']; ?>img/thumbs/<?php echo htmlspecialchars($post['thumbnail']); ?>" 
                                 alt="<?php echo htmlspecialchars($post['title']); ?>" class="post-thumb-img">
                        <?php endif; ?>
                    <?php else: ?>
                        <?php $placeholder_images = ['placeholder1.jpg', 'placeholder2.jpg', 'placeholder3.jpg', 'placeholder4.jpg']; ?>
                        <?php $random_placeholder = $placeholder_images[array_rand($placeholder_images)]; ?>
                        <img src="<?php echo $paths['base_path']; ?>img/thumbs/<?php echo $random_placeholder; ?>" 
                             alt="<?php echo htmlspecialchars($post['title']); ?>" class="post-thumb-img placeholder">
                    <?php endif; ?>
                </div>
                <div class="post-card-content">
                    <h3 class="post-card-title"><?php echo htmlspecialchars($post['title']); ?></h3>
                    <p class="post-card-excerpt"><?php echo htmlspecialchars($post['excerpt']); ?></p>
                    <div class="post-card-meta">
                        <?php if (isset($post['author']) && $post['author']): ?>
                            <span class="post-author">By <?php echo htmlspecialchars($post['author']); ?></span>
                        <?php endif; ?>
                        <?php if (isset($post['date']) && $post['date']): ?>
                            <span class="post-date"><?php echo is_string($post['date']) ? htmlspecialchars($post['date']) : ''; ?></span>
                        <?php endif; ?>
                    </div>
                    <?php if (!empty($post['tags'])): ?>
                        <div class="post-card-tags">
                            <?php foreach ($post['tags'] as $tag): ?>
                                <span class="tag"><?php echo htmlspecialchars($tag); ?></span>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
                </a>
            </div>
        <?php endforeach; ?>
    </div>
</div>
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>