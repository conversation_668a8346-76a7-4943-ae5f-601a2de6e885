<?php
// Set content type to JSON
header('Content-Type: application/json');

// Include necessary files
require_once '../path-helper.php';
$config = include '../config.php';
$paths = initPaths($config, __FILE__);
require_once $paths['base_path'] . 'includes/PathHelper.php';

// Function to scan content directory and build graph data
function buildGraphData($basePath) {
    $contentDir = $basePath . 'content';
    $nodes = [];
    $links = [];
    $nodeIds = [];
    $categories = [];
    
    // Scan content directory recursively
    $contentFiles = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($contentDir, RecursiveDirectoryIterator::SKIP_DOTS)
    );
    
    // First pass: collect all nodes
    foreach ($contentFiles as $file) {
        if ($file->getExtension() === 'php' && $file->getFilename() !== 'index.php' && $file->getFilename() !== 'debug-web.php') {
            $relativePath = str_replace('\\', '/', substr($file->getPathname(), strlen($basePath)));
            $url = '/' . $relativePath;
            
            // Extract category from path
            $pathParts = explode('/', $relativePath);
            $category = (count($pathParts) > 2) ? $pathParts[1] : 'default';
            
            // Store category for later use
            if (!in_array($category, $categories) && $category !== 'content') {
                $categories[] = $category;
            }
            
            // Get title from file content
            $title = getPageTitle($file->getPathname());
            $id = md5($relativePath); // Create unique ID
            
            $nodes[] = [
                'id' => $id,
                'title' => $title,
                'url' => $url,
                'category' => $category,
                'weight' => 1 // Default weight
            ];
            
            $nodeIds[$relativePath] = $id;
        }
    }
    
    // Second pass: establish links based on related posts
    foreach ($contentFiles as $file) {
        if ($file->getExtension() === 'php' && $file->getFilename() !== 'index.php' && $file->getFilename() !== 'debug-web.php') {
            $relativePath = str_replace('\\', '/', substr($file->getPathname(), strlen($basePath)));
            $sourceId = $nodeIds[$relativePath];
            
            // Parse file to find related posts
            $relatedPosts = getRelatedPosts($file->getPathname());
            
            foreach ($relatedPosts as $relatedPost) {
                $relatedUrl = $relatedPost['url'];
                $relatedPath = convertUrlToPath($relatedUrl, $basePath);
                
                if (isset($nodeIds[$relatedPath])) {
                    $targetId = $nodeIds[$relatedPath];
                    
                    // Add link if not already exists
                    $linkExists = false;
                    foreach ($links as $link) {
                        if (($link['source'] === $sourceId && $link['target'] === $targetId) ||
                            ($link['source'] === $targetId && $link['target'] === $sourceId)) {
                            $linkExists = true;
                            break;
                        }
                    }
                    
                    if (!$linkExists && $sourceId !== $targetId) {
                        $links[] = [
                            'source' => $sourceId,
                            'target' => $targetId,
                            'value' => 1
                        ];
                    }
                }
            }
        }
    }
    
    // Update node weights based on number of connections
    $nodeLinkCounts = [];
    foreach ($links as $link) {
        if (!isset($nodeLinkCounts[$link['source']])) {
            $nodeLinkCounts[$link['source']] = 0;
        }
        if (!isset($nodeLinkCounts[$link['target']])) {
            $nodeLinkCounts[$link['target']] = 0;
        }
        $nodeLinkCounts[$link['source']]++;
        $nodeLinkCounts[$link['target']]++;
    }
    
    foreach ($nodes as &$node) {
        if (isset($nodeLinkCounts[$node['id']])) {
            $node['weight'] = min(3, $nodeLinkCounts[$node['id']] / 5 + 1);
        }
    }
    
    return [
        'nodes' => $nodes,
        'links' => $links,
        'categories' => $categories
    ];
}

// Helper function to extract page title from PHP file
function getPageTitle($filePath) {
    $content = file_get_contents($filePath);
    if (preg_match('/\$page_title\s*=\s*[\'"](.+?)[\'"]/s', $content, $matches)) {
        $title = $matches[1];
        // Clean up multi-line titles
        $title = preg_replace('/\s*\n\s*"[^"]*"\s*;?$/m', '', $title);
        return $title;
    }
    return basename($filePath, '.php');
}

// Helper function to extract related posts from PHP file
function getRelatedPosts($filePath) {
    $content = file_get_contents($filePath);
    $relatedPosts = [];
    
    if (preg_match('/\$related_posts\s*=\s*\[(.*?)\];/s', $content, $matches)) {
        $relatedPostsStr = $matches[1];
        
        // Extract individual related post entries
        preg_match_all('/\[\s*\'title\'\s*=>\s*\'(.*?)\'\s*,\s*\'url\'\s*=>\s*(.*?)\s*,/s', $relatedPostsStr, $postMatches, PREG_SET_ORDER);
        
        foreach ($postMatches as $match) {
            $url = trim($match[2]);
            // Clean up URL - remove variables and quotes
            $url = preg_replace('/PathHelper::getCategoryIndexPath\([\'"](.+?)[\'"]\)/', 'content/$1/index.php', $url);
            $url = preg_replace('/[\'"](.*?)[\'"]/s', '$1', $url);
            $url = preg_replace('/\s*\.\s*\$[a-zA-Z_]+\s*\.\s*/', '', $url);
            
            $relatedPosts[] = [
                'title' => $match[1],
                'url' => $url
            ];
        }
    }
    
    return $relatedPosts;
}

// Helper function to convert URL to file path
function convertUrlToPath($url, $basePath) {
    // Remove base URL if present
    $url = preg_replace('/^https?:\/\/[^\/]+/', '', $url);
    
    // Remove leading slash and add content/ if needed
    $url = ltrim($url, '/');
    if (!preg_match('/^content\//', $url)) {
        $url = 'content/' . $url;
    }
    
    return $url;
}

// Build and output graph data
$graphData = buildGraphData($paths['base_path']);
echo json_encode($graphData);