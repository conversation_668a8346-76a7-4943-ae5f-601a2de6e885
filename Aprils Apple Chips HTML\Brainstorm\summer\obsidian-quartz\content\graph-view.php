<?php
// Include necessary files
require_once '../path-helper.php';
$config = include '../config.php';
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Content Graph View';
$meta_description = 'Interactive visualization of all content connections on A. A. Chips';
$meta_keywords = 'graph, visualization, content map, connections';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [
    ['title' => 'Table of Contents', 'url' => $base_url . 'content/contents.php', 'excerpt' => 'View all content in list format']
];

// Generate content
ob_start();
?>

<h1>Content Graph View</h1>
<p>Explore connections between different content pieces. Hover over nodes to see titles, and click to navigate to that content.</p>

<div id="graph-container" style="width: 100%; height: 600px; border: 1px solid #eee; border-radius: var(--border-radius);"></div>

<script src="https://d3js.org/d3.v7.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Graph visualization will be implemented here
    const width = document.getElementById('graph-container').clientWidth;
    const height = document.getElementById('graph-container').clientHeight;
    
    // Create SVG container
    const svg = d3.select("#graph-container")
        .append("svg")
        .attr("width", width)
        .attr("height", height);
        
    // We'll fetch data and render the graph
    fetchGraphData();
    
    function fetchGraphData() {
        fetch('<?php echo $base_url; ?>api/graph-data.php')
            .then(response => response.json())
            .then(data => renderGraph(data))
            .catch(error => console.error('Error fetching graph data:', error));
    }
    
    function renderGraph(data) {
        // Create a force simulation
        const simulation = d3.forceSimulation(data.nodes)
            .force("link", d3.forceLink(data.links).id(d => d.id).distance(100))
            .force("charge", d3.forceManyBody().strength(-300))
            .force("center", d3.forceCenter(width / 2, height / 2))
            .force("collide", d3.forceCollide().radius(30));
            
        // Add links
        const link = svg.append("g")
            .selectAll("line")
            .data(data.links)
            .enter()
            .append("line")
            .attr("stroke", "#999")
            .attr("stroke-opacity", 0.6)
            .attr("stroke-width", d => Math.sqrt(d.value));
            
        // Add nodes
        const node = svg.append("g")
            .selectAll("circle")
            .data(data.nodes)
            .enter()
            .append("circle")
            .attr("r", d => d.weight * 5 + 5)
            .attr("fill", d => categoryColor(d.category))
            .call(drag(simulation))
            .on("click", handleNodeClick);
            
        // Add node titles
        const nodeLabels = svg.append("g")
            .selectAll("text")
            .data(data.nodes)
            .enter()
            .append("text")
            .text(d => d.title)
            .attr("font-size", "10px")
            .attr("dx", 12)
            .attr("dy", 4)
            .style("pointer-events", "none")
            .style("opacity", 0);
            
        // Node hover effects
        node.on("mouseover", function(event, d) {
            d3.select(this).attr("stroke", "#333").attr("stroke-width", 2);
            d3.select(this.parentNode.parentNode)
                .selectAll("text")
                .filter(textD => textD.id === d.id)
                .style("opacity", 1);
        }).on("mouseout", function(event, d) {
            d3.select(this).attr("stroke", null);
            d3.select(this.parentNode.parentNode)
                .selectAll("text")
                .filter(textD => textD.id === d.id)
                .style("opacity", 0);
        });
        
        // Update positions on simulation tick
        simulation.on("tick", () => {
            link
                .attr("x1", d => d.source.x)
                .attr("y1", d => d.source.y)
                .attr("x2", d => d.target.x)
                .attr("y2", d => d.target.y);
                
            node
                .attr("cx", d => d.x)
                .attr("cy", d => d.y);
                
            nodeLabels
                .attr("x", d => d.x)
                .attr("y", d => d.y);
        });
        
        // Helper functions
        function categoryColor(category) {
            const colors = {
                'alienation': '#6b5b95',
                'ash': '#88b04b',
                'climate': '#5b9aa0',
                'humor': '#f7786b',
                'inspiration': '#c06c84',
                'journal': '#f18973',
                'judaism': '#3498db',
                'kitchen': '#e67e22',
                'ptsd-myth': '#8e44ad',
                'street': '#9b59b6',
                'writings': '#2ecc71',
                'default': '#95a5a6'
            };
            return colors[category] || colors.default;
        }
        
        function handleNodeClick(event, d) {
            if (d.url) {
                window.location.href = d.url;
            }
        }
        
        function drag(simulation) {
            function dragstarted(event) {
                if (!event.active) simulation.alphaTarget(0.3).restart();
                event.subject.fx = event.subject.x;
                event.subject.fy = event.subject.y;
            }
            
            function dragged(event) {
                event.subject.fx = event.x;
                event.subject.fy = event.y;
            }
            
            function dragended(event) {
                if (!event.active) simulation.alphaTarget(0);
                event.subject.fx = null;
                event.subject.fy = null;
            }
            
            return d3.drag()
                .on("start", dragstarted)
                .on("drag", dragged)
                .on("end", dragended);
        }
    }
});
</script>
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>
