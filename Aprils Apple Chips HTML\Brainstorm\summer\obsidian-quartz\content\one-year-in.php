<?php
// Auto-generated blog post
// Source: one-year-in.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'One year into being a Kindergarten teacher, and what that\'s like.';
$meta_description = 'It\'s been one year since I started teaching Kindergarten. I am reflecting on what that\'s like.';
$meta_keywords = 'kindergarten, teaching, journal, personal, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'One year into being a Kindergarten teacher, and what that\'s like.',
  'author' => 'A. A. Chips',
  'date' => '2025-05-11',
  'tags' => 
  array (
    0 => 'kindergarten',
    1 => 'teaching',
    2 => 'journal',
    3 => 'personal',
  ),
  'excerpt' => 'It\'s been one year since I started teaching Kindergarten. I am reflecting on what that\'s like.',
  'source_file' => 'content\\one-year-in.md',
);

// Raw content
$post_content = '<p>It\'s been one academic school year since I started after school teaching. Every day I am surrounded by teachers who have been at this for far longer. The pay is terrible. But in every other way it is a delight. It\'s also a roller coaster of challenges. I\'m challenged every day to improve myself to be a better version of myself so I can model that for my students. I build meaningful connection with my students, my co-teachers, the administrators, and the parents of the students. I could go on about this, but I\'d rather share a few of the Kiddisms I have collected.</p>

<img src="img/kindergarten12.jpg" alt="kindergarten12.jpg">

<p>Kid gets called to go home.</p>
<p>"Argh. I don\'t want to go home. Did you know I am scared of my bedroom?"</p>
<p>"What? Why? Is it haunted or something?"</p>
<p>"Yes.. it is haunted. By me. I am a vampire."</p>

<p>We\'re in line to go to the gym.</p>
<p>Kid has one of those flat translucent marbles, like the ones you play Mancala with.</p>
<p>"I\'m going to look at you! Oh my god you are so creepy looking! I can see your skin..."</p>

<p>Kids are hungry, it\'s before snack time. They are pulling on the new snack bag to find out what\'s for snack. We are fending off these snack bag attacks. The snack of the day is Fig Newtons. Two kids are caught trying to pull on the bag and sneak a look at the snack.</p>

<p>Kid 1: "I just want to know what is for snack today."</p>
<p>Kid 2: "Well, I actually know what\'s for snack, but I don\'t know what they are called."</p>
<p>Me: "Well, if you had to guess what they are called, so you can tell your classmate, what would you guess they are called?"</p>
<p>Kid 2: "I don\'t know.. Boobabs?"</p>

<p>Kiddism 2. Out of context eavesdropping into kid-conversation at the wrong/right time: "It started out all gross and disgusting, but now it\'s nice and wonderful."</p>';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>